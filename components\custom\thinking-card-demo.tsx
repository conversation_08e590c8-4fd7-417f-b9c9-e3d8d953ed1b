"use client";

import { useState } from "react";
import { ThinkingCard } from "./thinking-card";

export const ThinkingCardDemo = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [content, setContent] = useState("问题已提交，模型加载中");
  const [isLoading, setIsLoading] = useState(false);

  const startDemo = () => {
    setIsVisible(true);
    setIsLoading(true);
    setContent("问题已提交，模型加载中");

    // 模拟工具调用
    setTimeout(() => {
      setContent(prev => prev + "\n正在查询知识图谱");
    }, 1000);

    setTimeout(() => {
      setContent(prev => prev + "\n正在生成图表");
    }, 2000);

    setTimeout(() => {
      setIsLoading(false);
      setIsVisible(false);
    }, 3000);
  };

  return (
    <div className="p-4 space-y-4">
      <button
        onClick={startDemo}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        测试 Thinking 卡片
      </button>
      
      <ThinkingCard
        isVisible={isVisible}
        content={content}
        isLoading={isLoading}
      />
    </div>
  );
};
