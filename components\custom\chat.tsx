"use client";

import { Attachment, Message, CreateMessage, ChatRequestOptions } from "ai";
import { useState, useCallback, useRef, useEffect } from "react";

import { Message as PreviewMessage } from "@/components/custom/message";
import { useScrollToBottom } from "@/components/custom/use-scroll-to-bottom";
import { ThinkingCard } from "@/components/custom/thinking-card";

import { MultimodalInput } from "./multimodal-input";
import { Overview } from "./overview";

export function Chat({
  id,
  type,
  initialMessages,
}: {
  id: string;
  type: string;
  initialMessages: Array<Message>;
}) {
  
  const [messages, setMessages] = useState<Array<Message>>(initialMessages);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  // alert(JSON.stringify(messages))
  const [messagesContainerRef, messagesEndRef] =
    useScrollToBottom<HTMLDivElement>();

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);

  // Thinking卡片状态
  const [showThinkingCard, setShowThinkingCard] = useState(false);
  const [thinkingContent, setThinkingContent] = useState("问题已提交，模型加载中");
  const [isThinkingLoading, setIsThinkingLoading] = useState(false);
  const [streamCompleted, setStreamCompleted] = useState(false);

  // 用于管理打字机效果的定时器
  const typewriterTimers = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // --- START: NEW FUNCTIONS FOR SEQUENTIAL TYPING ---

  // 新增: 封装单个消息打字机效果的函数，返回一个 Promise
  // 这个函数负责为一条消息执行完整的打字动画
  const typeMessage = useCallback((message: Message) => {
    return new Promise<void>(resolve => {
      // 1. 先将空消息添加到消息列表，为打字做准备
      setMessages(prev => [...prev, { ...message, content: "" }]);

      // 2. 开始打字机效果
      const content = message.content;
      let currentIndex = 0;

      const typewriterInterval = setInterval(() => {
        if (currentIndex < content.length) {
          // 你的智能分割逻辑 (保持不变)
          let charsToAdd = "";
          const char = content[currentIndex];

          if (/[\u4e00-\u9fff]/.test(char)) {
            // 中文字符，一次显示1个
            charsToAdd = char;
            currentIndex += 1;
          } else {
            // 英文字符，一次显示2-3个或到空格/标点
            let endIndex = currentIndex + 3;
            for (let i = currentIndex; i < Math.min(endIndex, content.length); i++) {
              if (/[\s.,!?;:]/.test(content[i])) {
                endIndex = i + 1;
                break;
              }
            }
            charsToAdd = content.slice(currentIndex, Math.min(endIndex, content.length));
            currentIndex += charsToAdd.length;
          }

          // 更新指定消息的内容
          setMessages(prev => prev.map(msg =>
            msg.id === message.id
              ? { ...msg, content: msg.content + charsToAdd }
              : msg
          ));
        } else {
          // 3. 打字结束，清理定时器
          clearInterval(typewriterInterval);
          typewriterTimers.current.delete(message.id);
          
          // 4. 关键: 调用 resolve()，通知 Promise 已完成，可以进行下一步
          resolve(); 
        }
      }, 80); // 打字速度

      // 保存定时器引用，以便可以中途跳过
      typewriterTimers.current.set(message.id, typewriterInterval);
    });
  }, []); // setMessages 和 useRef 是稳定的，因此依赖数组为空

  // 新增: 创建一个 async 函数来按顺序处理所有消息
  // 这个函数会循环等待每条消息都打完字
  const processMessagesSequentially = useCallback(async (messagesToProcess: Message[]) => {
    // 使用 for...of 循环，它能与 await 完美配合
    for (const message of messagesToProcess) {
      // await 会暂停循环，直到 typeMessage 函数的 Promise 完成
      await typeMessage(message);
      // (可选) 在每条消息打完后增加一个短暂的停顿，改善体验
      await new Promise(res => setTimeout(res, 200)); 
    }
  }, [typeMessage]); // 此函数依赖于上面定义的 typeMessage

  // --- END: NEW FUNCTIONS FOR SEQUENTIAL TYPING ---

  // 跳过打字机效果的函数
  const skipTypewriter = useCallback((messageId: string, fullContent: string) => {
    const timer = typewriterTimers.current.get(messageId);
    if (timer) {
      clearInterval(timer);
      typewriterTimers.current.delete(messageId);
      // 立即显示完整内容
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? { ...msg, content: fullContent }
          : msg
      ));
    }
  }, []);

  // 组件卸载时清理所有定时器
  useEffect(() => {
    return () => {
      typewriterTimers.current.forEach((timer) => {
        clearInterval(timer);
      });
      typewriterTimers.current.clear();
    };
  }, []);

  // 自定义的提交函数，调用新的 mychat 接口
  const handleSubmit = useCallback(async (event?: { preventDefault?: () => void }) => {
    if (event?.preventDefault) {
      event.preventDefault();
    }

    if (!input.trim() || isLoading) {
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input,
    };

    // 添加用户消息到聊天
    setMessages(prev => [...prev, userMessage]);
    const currentInput = input;

    setInput("");
    setIsLoading(true);

    // 显示Thinking卡片
    setShowThinkingCard(true);
    setThinkingContent("问题已提交，模型加载中");
    setIsThinkingLoading(true);
    setStreamCompleted(false);
    const dealMessages: Message[] = []; // 明确类型

    const currentMessages = [...messages, userMessage];
    // alert(JSON.stringify(currentMessages)); // 建议在开发时使用 console.log

    try {
      // 调用新的 mychat 接口
      const response = await fetch('/api/mychat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id:id, type:type, currentInput: currentInput, currentMessages:currentMessages }),
      });

      if (!response.ok) {
        throw new Error('Network response was not ok');
      }

      // 处理流式响应
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            // 流式响应完成，隐藏Thinking卡片，开始显示消息
            setIsThinkingLoading(false);
            setStreamCompleted(true);
            setShowThinkingCard(false);

            console.log('Stream done. Processing messages sequentially. Count:', dealMessages.length);
            
            // --- START: MODIFICATION ---
            // 调用新的序贯处理函数，代替旧的 forEach 逻辑
            // 这个函数会保证消息一个接一个地显示
            processMessagesSequentially(dealMessages);
            // --- END: MODIFICATION ---

            break;
          }

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                console.log('Stream completed');
                break;
              }

              try {
                const parsedData = JSON.parse(data);
                console.log('Received chunk:', parsedData);

                // 更新Thinking卡片内容
                if (parsedData.role === "tool") {
                  if (parsedData.name === "neo4j_search") {
                    setThinkingContent(prev => prev + "\n正在查询知识图谱");
                  } else if (parsedData.name === "chart") {
                    setThinkingContent(prev => prev + "\n正在生成图表");
                  }
                }

                const content = parsedData.content;
                const messageId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

                // 先添加消息到待处理队列
                const newMessage: Message = {
                  id: messageId,
                  role: parsedData.role,
                  name: parsedData.name,
                  content: content,
                };
                console.log('Adding message to pending queue:', newMessage);
                dealMessages.push(newMessage)
              } catch (e) {
                console.error('Error parsing chunk:', e);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error calling mychat API:', error);

      // 添加错误消息
      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        role: "assistant",
        content: "抱歉，发生了错误。请稍后再试。",
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [input, isLoading, messages, id, type, processMessagesSequentially]); // 添加 processMessagesSequentially 到依赖数组

  const stop = useCallback(() => {
    setIsLoading(false);
  }, []);

  const append = useCallback(async (message: Message | CreateMessage, chatRequestOptions?: ChatRequestOptions) => {
    const newMessage: Message = {
      id: message.id || Date.now().toString(),
      role: message.role as "user" | "assistant" | "system" | "function" | "data" | "tool",
      content: message.content,
    };
    setMessages(prev => [...prev, newMessage]);
    return newMessage.id;
  }, []);

  return (
    <div className="flex flex-row justify-center pb-4 md:pb-8 h-dvh bg-background">
      <div className="flex flex-col justify-between items-center gap-4">
        <div
          ref={messagesContainerRef}
          className="flex flex-col gap-4 h-full w-dvw items-center overflow-y-scroll"
        >
          {messages.length === 5 && <Overview />}
          
          {messages.map((message) => (
            <PreviewMessage
              key={message.id}
              chatId={id}
              role={message.role}
              name={message.name || ""}
              content={message.content}
              attachments={message.experimental_attachments}
              toolInvocations={message.toolInvocations}
            />
          ))}

          {/* Thinking卡片 */}
          <ThinkingCard
            isVisible={showThinkingCard}
            content={thinkingContent}
            isLoading={isThinkingLoading}
          />

          <div
            ref={messagesEndRef}
            className="shrink-0 min-w-[24px] min-h-[24px]"
          />
        </div>

        <form className="flex flex-row gap-2 relative items-end w-full md:max-w-[500px] max-w-[calc(100dvw-32px) px-4 md:px-0">
          <MultimodalInput
            input={input}
            setInput={setInput}
            handleSubmit={handleSubmit}
            isLoading={isLoading}
            stop={stop}
            attachments={attachments}
            setAttachments={setAttachments}
            messages={messages}
            append={append}
          />
        </form>
      </div>
    </div>
  );
}