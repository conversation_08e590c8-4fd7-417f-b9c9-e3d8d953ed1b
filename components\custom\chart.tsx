"use client";

import React from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';

interface ChartData {
  labels: string[];
  data: number[];
  type?: 'bar' | 'pie';
  title?: string;
}

// 示例数据格式：
// {
//   "labels": ["苹果", "香蕉", "橙子", "葡萄"],
//   "data": [30, 25, 20, 15],
//   "type": "pie",
//   "title": "水果销量分布"
// }

// 预定义的颜色方案
const COLORS = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B',
  '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16',
  '#F97316', '#6366F1', '#14B8A6', '#F59E0B'
];

interface ChartProps {
  chartData?: ChartData;
}

export function Chart({ chartData }: ChartProps) {
  // 如果没有数据，显示加载状态
  if (!chartData) {
    return (
      <div className="w-full h-64 bg-muted rounded-lg flex items-center justify-center">
        <div className="text-muted-foreground">Loading chart...</div>
      </div>
    );
  }

  const { labels, data, type = 'bar', title } = chartData;

  // 转换数据格式为 Recharts 需要的格式
  const chartDataFormatted = labels.map((label, index) => ({
    name: label,
    value: data[index],
  }));

  // 使用 Recharts 的柱状图实现
  if (type === 'bar') {
    return (
      <div className="w-full p-4 bg-white dark:bg-gray-800 rounded-lg border">
        {title && (
          <h3 className="text-lg font-semibold mb-4 text-center text-gray-800 dark:text-gray-200">{title}</h3>
        )}
        <ResponsiveContainer width="100%" height={300}>
          <BarChart
            data={chartDataFormatted}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
            <XAxis
              dataKey="name"
              tick={{ fontSize: 12, fill: '#666' }}
              axisLine={{ stroke: '#e0e0e0' }}
            />
            <YAxis
              tick={{ fontSize: 12, fill: '#666' }}
              axisLine={{ stroke: '#e0e0e0' }}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: '#f8f9fa',
                border: '1px solid #e0e0e0',
                borderRadius: '6px',
                fontSize: '12px'
              }}
            />
            <Bar
              dataKey="value"
              fill="#3B82F6"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    );
  }

  // 使用 Recharts 的饼图实现
  if (type === 'pie') {
    return (
      <div className="w-full p-4 bg-white dark:bg-gray-800 rounded-lg border">
        {title && (
          <h3 className="text-lg font-semibold mb-4 text-center text-gray-800 dark:text-gray-200">{title}</h3>
        )}
        <ResponsiveContainer width="100%" height={350}>
          <PieChart>
            <Pie
              data={chartDataFormatted}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }: any) => `${name}: ${(percent * 100).toFixed(1)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {chartDataFormatted.map((_, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip
              contentStyle={{
                backgroundColor: '#f8f9fa',
                border: '1px solid #e0e0e0',
                borderRadius: '6px',
                fontSize: '12px'
              }}
            />
            <Legend
              verticalAlign="bottom"
              height={36}
              wrapperStyle={{ fontSize: '12px' }}
            />
          </PieChart>
        </ResponsiveContainer>
      </div>
    );
  }

  return null;
}
