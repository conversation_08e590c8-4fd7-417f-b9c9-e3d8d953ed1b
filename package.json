{"name": "gemini-chatbot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "tsx db/migrate && next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/google": "^0.0.51", "@ai-sdk/openai": "^1.3.22", "@langchain/langgraph-sdk": "^0.0.84", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@radix-ui/react-visually-hidden": "^1.1.0", "@vercel/analytics": "^1.3.1", "@vercel/blob": "^0.24.1", "@vercel/postgres": "^0.10.0", "ai": "3.4.9", "bcrypt-ts": "^5.0.2", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.34.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-tailwindcss": "^3.17.5", "framer-motion": "^11.3.19", "geist": "^1.3.1", "input-otp": "^1.2.4", "lucide-react": "^0.446.0", "next": "15.0.0-canary.152", "next-auth": "5.0.0-beta.22", "next-themes": "^0.3.0", "postgres": "^3.4.4", "react": "19.0.0-rc-7771d3a7-20240827", "react-dom": "19.0.0-rc-7771d3a7-20240827", "react-markdown": "^9.0.1", "recharts": "^3.1.0", "remark-gfm": "^4.0.0", "server-only": "^0.0.1", "sonner": "^1.5.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "zod": "^3.23.8"}, "devDependencies": {"@types/d3-scale": "^4.0.8", "@types/node": "^20", "@types/pdf-parse": "^1.1.4", "@types/react": "^18", "@types/react-dom": "^18", "drizzle-kit": "^0.25.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.31.0", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.1", "typescript": "^5"}}