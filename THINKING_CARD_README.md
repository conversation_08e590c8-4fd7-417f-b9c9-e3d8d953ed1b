# Thinking 卡片功能实现

## 功能概述

实现了一个智能的 Thinking 卡片功能，当用户提出问题时会显示在聊天界面左侧，并根据后端工具调用动态更新内容。

## 主要特性

### 1. 动态显示逻辑
- 用户提交问题时立即显示 Thinking 卡片
- 卡片初始内容为"问题已提交，模型加载中"
- 根据接收到的工具调用消息动态更新内容
- 流式响应完成后隐藏卡片，开始显示实际消息

### 2. 工具识别
- `neo4j_search` 工具调用时显示"正在查询知识图谱"
- `chart` 工具调用时显示"正在生成图表"
- 可以轻松扩展支持更多工具类型

### 3. 视觉效果
- 三个点的流动加载动画
- 卡片可展开/收缩
- 平滑的进入/退出动画
- 渐变背景和阴影效果
- 响应式设计

### 4. 消息流控制
- Thinking 卡片显示期间，其他消息暂存在待处理队列
- 流式响应完成后才开始逐个显示消息
- 保持原有的打字机效果

## 文件结构

```
components/custom/
├── thinking-card.tsx          # Thinking 卡片组件
├── thinking-card-demo.tsx     # 演示组件
└── chat.tsx                   # 主聊天组件（已修改）
```

## 核心组件

### ThinkingCard 组件

```typescript
interface ThinkingCardProps {
  isVisible: boolean;    // 是否显示卡片
  content: string;       // 卡片内容
  isLoading: boolean;    // 是否显示加载动画
}
```

### 主要状态管理

```typescript
// Thinking卡片状态
const [showThinkingCard, setShowThinkingCard] = useState(false);
const [thinkingContent, setThinkingContent] = useState("问题已提交，模型加载中");
const [isThinkingLoading, setIsThinkingLoading] = useState(false);
const [streamCompleted, setStreamCompleted] = useState(false);
const [pendingMessages, setPendingMessages] = useState<Array<Message>>([]);
```

## 工作流程

1. **用户提交问题**
   - 显示 Thinking 卡片
   - 设置初始内容和加载状态
   - 开始流式请求

2. **接收工具调用**
   - 检测 `role === "tool"` 的消息
   - 根据 `name` 字段更新卡片内容
   - 将消息添加到待处理队列

3. **流式响应完成**
   - 隐藏 Thinking 卡片
   - 开始处理待处理消息队列
   - 逐个显示消息并应用打字机效果

## 样式特性

- 使用 Framer Motion 实现流畅动画
- 蓝色渐变主题配色
- 支持深色模式
- 响应式布局
- 可展开/收缩的交互设计

## 扩展性

可以轻松添加更多工具类型的识别：

```typescript
if (parsedData.role === "tool") {
  if (parsedData.name === "neo4j_search") {
    setThinkingContent(prev => prev + "\n正在查询知识图谱");
  } else if (parsedData.name === "chart") {
    setThinkingContent(prev => prev + "\n正在生成图表");
  } else if (parsedData.name === "your_new_tool") {
    setThinkingContent(prev => prev + "\n正在执行新工具");
  }
}
```

## 使用方法

1. 组件已集成到主聊天界面
2. 用户提问时自动触发
3. 无需额外配置

## 演示

可以使用 `ThinkingCardDemo` 组件来测试卡片功能：

```tsx
import { ThinkingCardDemo } from "@/components/custom/thinking-card-demo";

// 在页面中使用
<ThinkingCardDemo />
```
