FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/xinghanxu/mcsm:ubuntu22.04

WORKDIR /chatbot-dir
RUN pwd

# 更新包管理器并安装基础依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    vim \
    build-essential \
    software-properties-common \
    && rm -rf /var/lib/apt/lists/*

# 安装Node.js 22.16.0 (使用NodeSource官方源)
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - \
    && apt-get install -y nodejs=22.16.0-1nodesource1

# 安装pnpm
RUN npm install -g pnpm

RUN git clone http://192.168.1.98:9980/project-group/gemini-chatbot.git

WORKDIR /chatbot-dir/gemini-chatbot
RUN pnpm install
EXPOSE 8094
CMD ["pnpm", "dev", "--port", "8094"]