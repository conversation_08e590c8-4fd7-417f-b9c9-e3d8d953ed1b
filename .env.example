# Get your Google Gemini API Key here https://cloud.google.com/vertex-ai
GOOGLE_GENERATIVE_AI_API_KEY=****

# Generate a random secret: https://generate-secret.vercel.app/32 or `openssl rand -base64 32`
AUTH_SECRET=****

/*
 * The following keys below are automatically created and
 * added to your environment when you deploy on vercel
 */

# Instructions to create kv database here: https://vercel.com/docs/storage/vercel-blob
BLOB_READ_WRITE_TOKEN=****

# Instructions to create a database here: https://vercel.com/docs/storage/vercel-postgres/quickstart
POSTGRES_URL=****
