import { google } from "@ai-sdk/google";
import { createOpenAI  } from '@ai-sdk/openai';
import { experimental_wrapLanguageModel as wrapLanguageModel } from "ai";

import { customMiddleware } from "./custom-middleware";

export const openai = createOpenAI({
  // custom settings, e.g.
  baseURL: "https://api.siliconflow.cn",
  apiKey: "sk-mqdiudcdydopxjrnwqgwbkauzgsljdireyvpipevdvghhhed",
  compatibility: 'strict' // strict mode, enable when using the OpenAI API
});

export const geminiProModel = wrapLanguageModel({
  model: google("gemini-1.5-pro-002"),
  middleware: customMiddleware,
});

export const geminiFlashModel = wrapLanguageModel({
  model: google("gemini-2.0-flash"),
  middleware: customMiddleware,
});
